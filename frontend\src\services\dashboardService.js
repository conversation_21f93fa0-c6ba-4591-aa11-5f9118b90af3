import api from './api';

// Dashboard service functions
export const dashboardService = {
  // Get dashboard statistics
  getDashboardStats: async () => {
    try {
      const response = await api.get('/dashboard/stats');
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Failed to fetch dashboard statistics' };
    }
  },

  // Get recent expenses
  getRecentExpenses: async (limit = 10) => {
    try {
      const response = await api.get(`/dashboard/recent-expenses?limit=${limit}`);
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Failed to fetch recent expenses' };
    }
  },

  // Get pending approvals (for admin users)
  getPendingApprovals: async () => {
    try {
      const response = await api.get('/dashboard/pending-approvals');
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Failed to fetch pending approvals' };
    }
  },

  // Get expense trends
  getExpenseTrends: async (period = '6months') => {
    try {
      const response = await api.get(`/dashboard/trends?period=${period}`);
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Failed to fetch expense trends' };
    }
  },

  // Get category breakdown
  getCategoryBreakdown: async (period = 'current_month') => {
    try {
      const response = await api.get(`/dashboard/category-breakdown?period=${period}`);
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Failed to fetch category breakdown' };
    }
  },
};
