import React, { useState, useEffect } from 'react';
import {
  <PERSON>rid,
  Card,
  CardContent,
  Typography,
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  AttachMoney,
  Receipt,
  Pending,
  CheckCircle,
  Cancel,
  Visibility,
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import { dashboardService, expenseService } from '../services';
import { Loading, ErrorMessage } from '../components';

const Dashboard = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState(null);
  const [recentExpenses, setRecentExpenses] = useState([]);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // For now, we'll use mock data since the backend endpoints don't exist yet
      // In a real implementation, you would call:
      // const [statsData, expensesData] = await Promise.all([
      //   dashboardService.getDashboardStats(),
      //   dashboardService.getRecentExpenses(5)
      // ]);

      // Mock data for demonstration
      const mockStats = {
        totalExpenses: 12450.75,
        monthlyExpenses: 3250.50,
        pendingExpenses: 5,
        approvedExpenses: 23,
        rejectedExpenses: 2,
        monthlyChange: 15.3,
      };

      const mockRecentExpenses = [
        {
          _id: '1',
          title: 'Office Supplies',
          amount: 125.50,
          category: 'Office',
          status: 'pending',
          createdAt: new Date().toISOString(),
        },
        {
          _id: '2',
          title: 'Business Lunch',
          amount: 85.00,
          category: 'Meals',
          status: 'approved',
          createdAt: new Date(Date.now() - 86400000).toISOString(),
        },
        {
          _id: '3',
          title: 'Travel Expenses',
          amount: 450.25,
          category: 'Travel',
          status: 'pending',
          createdAt: new Date(Date.now() - *********).toISOString(),
        },
        {
          _id: '4',
          title: 'Software License',
          amount: 299.99,
          category: 'Software',
          status: 'approved',
          createdAt: new Date(Date.now() - *********).toISOString(),
        },
        {
          _id: '5',
          title: 'Conference Ticket',
          amount: 750.00,
          category: 'Training',
          status: 'rejected',
          createdAt: new Date(Date.now() - *********).toISOString(),
        },
      ];

      setStats(mockStats);
      setRecentExpenses(mockRecentExpenses);
    } catch (err) {
      setError(err.message || 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'approved':
        return 'success';
      case 'rejected':
        return 'error';
      case 'pending':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'approved':
        return <CheckCircle fontSize="small" />;
      case 'rejected':
        return <Cancel fontSize="small" />;
      case 'pending':
        return <Pending fontSize="small" />;
      default:
        return null;
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  if (loading) {
    return <Loading message="Loading dashboard..." />;
  }

  if (error) {
    return (
      <ErrorMessage
        error={error}
        title="Dashboard Error"
        onRetry={fetchDashboardData}
      />
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Welcome back, {user?.username}!
      </Typography>
      <Typography variant="body1" color="textSecondary" gutterBottom>
        Here's an overview of your expense activity
      </Typography>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Total Expenses
                  </Typography>
                  <Typography variant="h5">
                    {formatCurrency(stats.totalExpenses)}
                  </Typography>
                </Box>
                <AttachMoney color="primary" fontSize="large" />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    This Month
                  </Typography>
                  <Typography variant="h5">
                    {formatCurrency(stats.monthlyExpenses)}
                  </Typography>
                  <Box display="flex" alignItems="center" mt={1}>
                    {stats.monthlyChange > 0 ? (
                      <TrendingUp color="error" fontSize="small" />
                    ) : (
                      <TrendingDown color="success" fontSize="small" />
                    )}
                    <Typography
                      variant="body2"
                      color={stats.monthlyChange > 0 ? 'error' : 'success'}
                      sx={{ ml: 0.5 }}
                    >
                      {Math.abs(stats.monthlyChange)}%
                    </Typography>
                  </Box>
                </Box>
                <Receipt color="secondary" fontSize="large" />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Pending
                  </Typography>
                  <Typography variant="h5">
                    {stats.pendingExpenses}
                  </Typography>
                </Box>
                <Pending color="warning" fontSize="large" />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Approved
                  </Typography>
                  <Typography variant="h5">
                    {stats.approvedExpenses}
                  </Typography>
                </Box>
                <CheckCircle color="success" fontSize="large" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Recent Expenses Table */}
      <Paper sx={{ width: '100%', overflow: 'hidden' }}>
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Recent Expenses
          </Typography>
        </Box>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Title</TableCell>
                <TableCell>Category</TableCell>
                <TableCell align="right">Amount</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Date</TableCell>
                <TableCell align="center">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {recentExpenses.map((expense) => (
                <TableRow key={expense._id} hover>
                  <TableCell>
                    <Typography variant="body2" fontWeight="medium">
                      {expense.title}
                    </Typography>
                  </TableCell>
                  <TableCell>{expense.category}</TableCell>
                  <TableCell align="right">
                    <Typography variant="body2" fontWeight="medium">
                      {formatCurrency(expense.amount)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      icon={getStatusIcon(expense.status)}
                      label={expense.status.charAt(0).toUpperCase() + expense.status.slice(1)}
                      color={getStatusColor(expense.status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>{formatDate(expense.createdAt)}</TableCell>
                  <TableCell align="center">
                    <IconButton size="small" color="primary">
                      <Visibility fontSize="small" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
    </Box>
  );
};

export default Dashboard;
