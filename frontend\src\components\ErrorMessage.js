import React from 'react';
import { <PERSON><PERSON>, Al<PERSON>Title, <PERSON>, Button } from '@mui/material';
import { Refresh as RefreshIcon } from '@mui/icons-material';

const ErrorMessage = ({
  error,
  title = 'Error',
  onRetry = null,
  severity = 'error',
  variant = 'filled'
}) => {
  const errorMessage = typeof error === 'string' ? error : error?.message || 'An unexpected error occurred';

  return (
    <Box sx={{ my: 2 }}>
      <Alert severity={severity} variant={variant}>
        <AlertTitle>{title}</AlertTitle>
        {errorMessage}
        {onRetry && (
          <Box sx={{ mt: 2 }}>
            <Button
              size="small"
              startIcon={<RefreshIcon />}
              onClick={onRetry}
              variant="outlined"
              color={severity}
            >
              Try Again
            </Button>
          </Box>
        )}
      </Alert>
    </Box>
  );
};

export default ErrorMessage;
