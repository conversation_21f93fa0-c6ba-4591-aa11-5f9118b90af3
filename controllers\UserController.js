const Usermodel= require('../models/User');

//create new user 
exports.createUser = async (req, res) => {
    try {
        const { username, email, password, role } = req.body;
        const allowedRoles = ['admin', 'employee', 'finance'];
        if (role && !allowedRoles.includes(role)) {
            return res.status(400).json({ message: 'Invalid role' });
        }
        const user = new Usermodel({ username, email, password, role });
        await user.save();
        res.status(200).json(user);
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
};


//get all users
exports.getAllUsers= async (req, res) => {
    try {
        const users= await Usermodel.find();
        res.status(200).json(users);
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
};

// get any user by id 
exports.getUserById = async (req, res) => {
  try {
    const userId = req.params.id;
    const user = await Usermodel.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    res.status(200).json(user);
  } catch (error) {
    console.error('Error fetching user by ID:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// delete user by id
exports.deleteUserById = async (req, res) => {
  const userId = req.params.id;
  try {
    const deletedUser = await Usermodel.findByIdAndDelete(userId);
    if (!deletedUser) {
      return res.status(404).json({ message: 'User not found' });
    }
    res.status(200).json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Error deleting user:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

//delete all users
exports.deleteAllusers = async (req, res) => {
  try {
    await Usermodel.deleteMany({});
    res.status(200).json({ message: 'All users deleted successfully' });
  } catch (error) {
    console.error('Error deleting all users:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};


//update user by id 
exports.updateUserById = async (req, res) => {
  const userId = req.params.id;
  const { username, email, password, role } = req.body;

  try {
    if (role !== undefined) {
      return res.status(400).json({ message: 'Role is not required here and cannot be changed' });
    }
    const updatedUser = await Usermodel.findByIdAndUpdate(
      userId,
      { username, email, password },
      { new: true }
    );
    if (!updatedUser) {
      return res.status(404).json({ message: 'User not found' });
    }
    res.status(200).json(updatedUser);
  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

