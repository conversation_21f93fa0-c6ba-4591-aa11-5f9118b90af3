import api from './api';

// Expense service functions
export const expenseService = {
  // Get all expenses with optional filters
  getExpenses: async (filters = {}) => {
    try {
      const params = new URLSearchParams();
      Object.keys(filters).forEach(key => {
        if (filters[key]) {
          params.append(key, filters[key]);
        }
      });

      const response = await api.get(`/expenses?${params.toString()}`);
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Failed to fetch expenses' };
    }
  },

  // Get expenses by user ID
  getExpensesByUser: async (userId) => {
    try {
      const response = await api.get(`/expenses/user/${userId}`);
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Failed to fetch user expenses' };
    }
  },

  // Get expense by ID
  getExpenseById: async (id) => {
    try {
      const response = await api.get(`/expenses/${id}`);
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Failed to fetch expense' };
    }
  },

  // Create new expense
  createExpense: async (expenseData) => {
    try {
      const response = await api.post('/expenses', expenseData);
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Failed to create expense' };
    }
  },

  // Update expense
  updateExpense: async (id, expenseData) => {
    try {
      const response = await api.put(`/expenses/${id}`, expenseData);
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Failed to update expense' };
    }
  },

  // Delete expense
  deleteExpense: async (id) => {
    try {
      const response = await api.delete(`/expenses/${id}`);
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Failed to delete expense' };
    }
  },

  // Get expense statistics
  getExpenseStats: async () => {
    try {
      const response = await api.get('/expenses/stats');
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Failed to fetch expense statistics' };
    }
  },

  // Upload receipt
  uploadReceipt: async (file) => {
    try {
      const formData = new FormData();
      formData.append('receipt', file);

      const response = await api.post('/expenses/upload-receipt', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Failed to upload receipt' };
    }
  },
};
