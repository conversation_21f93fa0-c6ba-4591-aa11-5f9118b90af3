// models/Reimbursement.js
const reimbursementSchema = new mongoose.Schema({
  expenseId: { type: mongoose.Schema.Types.ObjectId, ref: 'Expense', required: true },
  financeId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  status: { type: String, enum: ['pending', 'reimbursed'], default: 'pending' },
  paidOn: { type: Date },
  notes: { type: String }
});

module.exports = mongoose.model('Reimbursement', reimbursementSchema);