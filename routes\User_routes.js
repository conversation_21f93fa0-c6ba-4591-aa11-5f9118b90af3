const express= require('express');
const router= express.Router();
const userController= require('../controllers/UserController');

// Create a new user
router.post('/create', userController.createUser);

// Get all users
router.get('/all', userController.getAllUsers);

// Get a user by id
router.get('/:id', userController.getUserById);
    
// Delete all users
router.delete('/delete/all', userController.deleteAllusers);

// Delete a user by id
router.delete('/delete/:id', userController.deleteUserById);   

// Update a user by id
router.put('/update/:id', userController.updateUserById);


module.exports= router;